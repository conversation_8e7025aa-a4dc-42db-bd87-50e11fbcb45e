import 'package:flutter/material.dart';
import 'package:pie_chart/pie_chart.dart';
import 'dart:convert';

/// Abstract class for all widgets that can be represented as JSON.
abstract class JsonWidget {
  Widget build();
  Map<String, dynamic> toJson();
}

/// Text widget wrapper
class JsonText extends JsonWidget {
  final String text;
  final double? fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;

  JsonText(this.text, {this.fontSize, this.fontWeight, this.color, this.textAlign});

  @override
  Widget build() {
    return Text(
      text,
      textAlign: textAlign,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Text",
        "text": text,
        if (fontSize != null) "fontSize": fontSize,
        if (fontWeight != null) "fontWeight": fontWeight.toString(),
        if (color != null) "color": '#${color!.value.toRadixString(16)}',
        if (textAlign != null) "textAlign": textAlign.toString(),
      };
}

/// Card widget wrapper
class JsonCard extends JsonWidget {
  final JsonWidget child;
  final double? elevation;
  final double? borderRadius;

  JsonCard({required this.child, this.elevation, this.borderRadius});

  @override
  Widget build() {
    return Card(
      elevation: elevation ?? 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius ?? 8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child.build(),
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Card",
        if (elevation != null) "elevation": elevation,
        if (borderRadius != null) "borderRadius": borderRadius,
        "child": child.toJson(),
      };
}

/// Row widget wrapper
class JsonRow extends JsonWidget {
  final List<JsonWidget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  JsonRow({
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  });

  @override
  Widget build() {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children.map((e) => e.build()).toList(),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Row",
        "mainAxisAlignment": mainAxisAlignment.toString(),
        "crossAxisAlignment": crossAxisAlignment.toString(),
        "children": children.map((e) => e.toJson()).toList(),
      };
}

/// Column widget wrapper
class JsonColumn extends JsonWidget {
  final List<JsonWidget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  JsonColumn({
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build() {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children.map((e) => e.build()).toList(),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Column",
        "mainAxisAlignment": mainAxisAlignment.toString(),
        "crossAxisAlignment": crossAxisAlignment.toString(),
        "children": children.map((e) => e.toJson()).toList(),
      };
}

/// SizedBox widget wrapper
class JsonSizedBox extends JsonWidget {
  final double? width;
  final double? height;

  JsonSizedBox({this.width, this.height});

  @override
  Widget build() => SizedBox(width: width, height: height);

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "SizedBox",
        if (width != null) "width": width,
        if (height != null) "height": height,
      };
}

/// Expanded widget wrapper
class JsonExpanded extends JsonWidget {
  final JsonWidget child;
  final int flex;

  JsonExpanded({required this.child, this.flex = 1});

  @override
  Widget build() => Expanded(flex: flex, child: child.build());

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Expanded",
        "flex": flex,
        "child": child.toJson(),
      };
}

/// Container widget wrapper
class JsonContainer extends JsonWidget {
  final JsonWidget? child;
  final double? width;
  final double? height;
  final Color? color;

  JsonContainer({this.child, this.width, this.height, this.color});

  @override
  Widget build() => Container(
        width: width,
        height: height,
        color: color,
        child: child?.build(),
      );

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Container",
        if (width != null) "width": width,
        if (height != null) "height": height,
        if (color != null) "color": '#${color!.value.toRadixString(16)}',
        if (child != null) "child": child!.toJson(),
      };
}

/// Pie Chart widget wrapper (Leaf node using an external library)
class JsonPieChart extends JsonWidget {
  final Map<String, double> dataMap;
  final List<Color> colorList;
  final String centerText;
  final double chartRadius;
  final double ringStrokeWidth;

  JsonPieChart({
    required this.dataMap,
    required this.colorList,
    required this.chartRadius,
    required this.ringStrokeWidth,
    this.centerText = '',
  });

  @override
  Widget build() {
    return PieChart(
      dataMap: dataMap,
      colorList: colorList,
      chartType: ChartType.ring,
      chartRadius: chartRadius,
      ringStrokeWidth: ringStrokeWidth,
      centerWidget: Text(
        centerText,
        textAlign: TextAlign.center,
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: chartRadius / 8),
      ),
      legendOptions: const LegendOptions(showLegends: false),
      chartValuesOptions: const ChartValuesOptions(showChartValues: false),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "PieChart",
        "dataMap": dataMap,
        "colorList": colorList.map((c) => '#${c.value.toRadixString(16)}').toList(),
        "centerText": centerText,
        "chartRadius": chartRadius,
        "ringStrokeWidth": ringStrokeWidth,
      };
}

/// Legend widget wrapper composed of other JsonWidgets
class JsonLegend extends JsonWidget {
  final List<String> labels;
  final List<Color> colors;
  final double fontSize;
  
  final JsonWidget _root;

  JsonLegend({
    required this.labels,
    required this.colors,
    this.fontSize = 12,
  }) : _root = _buildLegend(labels, colors, fontSize);

  static JsonWidget _buildLegend(List<String> labels, List<Color> colors, double fontSize) {
    final numRows = (labels.length / 2).ceil();
    
    return JsonColumn(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(numRows, (rowIndex) {
        final int item1Index = rowIndex;
        final int item2Index = rowIndex + numRows;

        return JsonRow(
          children: [
            JsonExpanded(
              child: JsonRow(
                children: [
                  JsonContainer(width: 14, height: 14, color: colors[item1Index]),
                  JsonSizedBox(width: 6),
                  JsonText(labels[item1Index], fontSize: fontSize),
                ],
              ),
            ),
            JsonSizedBox(width: 18),
            JsonExpanded(
              child: item2Index < labels.length
                  ? JsonRow(
                      children: [
                        JsonContainer(width: 14, height: 14, color: colors[item2Index]),
                        JsonSizedBox(width: 6),
                        JsonText(labels[item2Index], fontSize: fontSize),
                      ],
                    )
                  : JsonSizedBox(), // Placeholder for alignment if odd number of items
            ),
          ],
        );
      }),
    );
  }

  @override
  Widget build() => _root.build();

  @override
  Map<String, dynamic> toJson() => {
        "widget_type": "Legend",
        "fontSize": fontSize,
        "composition": _root.toJson(),
      };
}

/// The main UI builder for the screen
class PieChartUIBuilder extends StatefulWidget {
  const PieChartUIBuilder({super.key});

  @override
  State<PieChartUIBuilder> createState() => PieChartUIBuilderState();
}

class PieChartUIBuilderState extends State<PieChartUIBuilder> {
  late JsonWidget screenWidgetTree;
  String jsonOutput = '';

  final Map<String, double> dataMap = {
    'Point 1': 1, 'Point 2': 1, 'Point 3': 1, 'Point 4': 1,
    'Point 5': 1, 'Point 6': 1, 'Point 7': 1, 'Point 8': 1,
  };

  final List<Color> colorList = [
    Color(0xFF0D47A1), Color(0xFF1565C0), Color(0xFF1976D2), Color(0xFF1E88E5),
    Color(0xFF42A5F5), Color(0xFF64B5F6), Color(0xFF90CAF9), Color(0xFFBBDEFB),
  ];

  JsonWidget buildChartSection({
    required String sectionTitle,
    required double headingFontSize,
    required double bodyFontSize,
    required double labelFontSize,
    required double chartRadius,
  }) {
    return JsonColumn(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        JsonText(sectionTitle, fontSize: 32, fontWeight: FontWeight.normal),
        JsonSizedBox(height: 12),
        JsonCard(
          elevation: 2,
          borderRadius: 12,
          child: JsonColumn(
            children: [
              JsonText("Temperature theme", fontSize: bodyFontSize, fontWeight: FontWeight.w600),
              JsonSizedBox(height: 8),
              JsonRow(
                children: [
                  JsonExpanded(
                    flex: 5,
                    child: JsonPieChart(
                      dataMap: dataMap,
                      colorList: colorList,
                      chartRadius: chartRadius,
                      ringStrokeWidth: chartRadius * 0.3,
                      centerText: "Total Value\n\$9,999.99",
                    ),
                  ),
                  JsonSizedBox(width: 24),
                  JsonExpanded(
                    flex: 4,
                    child: JsonLegend(
                      labels: dataMap.keys.toList(),
                      colors: colorList,
                      fontSize: labelFontSize,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        JsonSizedBox(height: 24),
        JsonText("Properties", fontWeight: FontWeight.bold, fontSize: bodyFontSize, color: Colors.black),
        JsonText("Heading 1 medium ${headingFontSize.toInt()}", fontSize: headingFontSize, fontWeight: FontWeight.w500),
        JsonText("Body 1 Regular ${bodyFontSize.toInt()}", fontSize: bodyFontSize),
        JsonText("Label - Regular ${labelFontSize.toInt()}", fontSize: labelFontSize, color: Colors.grey[700]),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    screenWidgetTree = JsonColumn(
      children: [
        JsonText('Properties - Typography', fontSize: 40, fontWeight: FontWeight.bold),
        JsonSizedBox(height: 32),
        JsonRow(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            JsonExpanded(
              child: buildChartSection(
                sectionTitle: "Small",
                headingFontSize: 14, bodyFontSize: 12, labelFontSize: 10, chartRadius: 80,
              ),
            ),
            JsonSizedBox(width: 32),
            JsonExpanded(
              child: buildChartSection(
                sectionTitle: "Medium",
                headingFontSize: 16, bodyFontSize: 14, labelFontSize: 12, chartRadius: 100,
              ),
            ),
            JsonSizedBox(width: 32),
            JsonExpanded(
              child: buildChartSection(
                sectionTitle: "Large",
                headingFontSize: 18, bodyFontSize: 16, labelFontSize: 12, chartRadius: 120,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<dynamic> generateJson() async {
    final generated = screenWidgetTree.toJson();
    setState(() {
      jsonOutput = const JsonEncoder.withIndent('  ').convert(generated);
    });
    print("=====================================   ${jsonEncode(generated)}  ===============================");
    return generated;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: screenWidgetTree.build(),
    );
  }
}
