import 'dart:convert';
import 'dart:developer';

import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:builder_app/features/login/presentation/bloc/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:builder_app/features/login/presentation/widgets/app_dropdown.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_bloc.dart';
import 'package:builder_app/features/login/presentation/bloc/app_config/config_event.dart';
import 'package:go_router/go_router.dart';
import 'package:builder_app/features/login/presentation/widgets/common_text_field.dart';
import 'package:builder_app/features/dashboard/widgets/pi_chart_card.dart';

import '../login/presentation/widgets/common_button.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;

class HomeScreen extends StatelessWidget {
  final String? welcomeMessage;
  final Map<String, dynamic>? userData;
  final int? selectedTab;
  final Map<String, dynamic>? settingsData;
  
  const HomeScreen({
    super.key,
    this.welcomeMessage,
    this.userData,
    this.selectedTab,
    this.settingsData,
  });

  @override
  Widget build(BuildContext context) {
    log("============ home builder");
    final isMobile = ResponsiveBreakpoints.of(context).isMobile;

    final sidebar = isMobile ? _SidebarDrawer() : _SidebarRail();
    final body =
    // EnhancedWidgetJsonExample();
    _buildResponsiveBody();

    if (isMobile) {
      return BlocProvider(
        create: (context) => HomeBloc()..add(FetchDropdownA()),
        child: Scaffold(
          appBar: AppBar(
            // backgroundColor: AppColors.textDark,
            title: Text(welcomeMessage ?? 'Bloc'),
            leading: Builder(
              builder: (context) => IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
          ),
          drawer: sidebar,
          body: body,
        ),
      );
    } else {
      // Desktop/tablet/web: sidebar always open, fixed width
      return BlocProvider(
        create: (context) => HomeBloc()..add(FetchDropdownA()),
        child: Scaffold(
          // appBar: AppBar(
          //    backgroundColor: AppColors.textDark,
          //   title: Text(welcomeMessage ?? 'Home'),
          // ),
          body: body
          // Row(
          //   children: [
          //     sidebar,
          //     // const VerticalDivider(width: 0.5),
          //     Expanded(
          //       flex: 8,
          //       child: Row(
          //         children: [
          //           Expanded(
          //             flex: 5,
          //             child: Column(
          //               children: [
          //                 Text(""),
          //               ],
          //             )),
          //               Expanded(
          //             flex: 3,
          //             child: SizedBox(child: Text(""),)),
          //         ],
          //       )),
          //         Expanded(
          //       flex: 2,
          //       child: SizedBox(child: Text(""),)),
          //   ],
          // ),
        ),
      );
    }
  }



Widget _buildResponsiveBody() {
  final pieChartKey = GlobalKey<ui_controls.PieChartUIBuilderState>();

  return Builder(
    builder: (context) {
      return Column(
        children: [
          Expanded(
            child: ui_controls.PieChartUIBuilder(key: pieChartKey),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CommonButton(
              text: 'Elevated Action',
              tooltip: 'Perform an action',
              onPressed: () async{
                // Access the chart data from the chart widget
                final jsonOutput =await pieChartKey.currentState?.generateJson();
                if (jsonOutput != null && context.mounted) {
                  log("Serialized PieChartData: $jsonOutput");
                  // You can use jsonOutput as needed
                  context.go("/json",extra: jsonOutput);
                }
              },
              child: const Text(
                "Next",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      );
    },
  );
}


  Widget _buildMobileLayout() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: MyTextFieldWidget(),
    );
  }

  Widget _buildTabletLayout() {
    return Center(
      child: const Padding(
        padding: EdgeInsets.all(24.0),
        child: MyTextFieldWidget(),
      ),
    );
  }

  Widget buildDesktopLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
           Text(
            'Properties - Typography',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 40,),
          ),
          const SizedBox(height: 32),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Small', style: TextStyle(fontSize: 32)),
                    const SizedBox(height: 12),
                    RingPieChartExample(
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Medium', style: TextStyle(fontSize: 32)),
                    const SizedBox(height: 12),
                    RingPieChartExample(
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Large', style: TextStyle(fontSize: 32)),
                    const SizedBox(height: 12),
                    RingPieChartExample(),
                  ],
                ),
              ),
              
            ],
          ),
             const SizedBox(height: 32),
          CommonButton(
            text: 'Elevated Action',
            tooltip: 'Perform an action',
            onPressed: () {
              context.go('/json');
              // ScaffoldMessenger.of(context).showSnackBar(
              //   SnackBar(content: Text('CommonButton pressed!')),
              // );
            },
          ),
          
        ],
      ),
    );
  }
}

class _SidebarDrawer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          IconButton(
            icon: const Icon(Icons.home),
            tooltip: 'Home',
            onPressed: () {
              context.go('/home');
            },
          ),
          // const SizedBox(height: 8),
          // IconButton(
          //   icon: const Icon(Icons.person),
          //   tooltip: 'Profile',
          //   onPressed: () {
          //     context.go('/profile/123', extra: {'userData': {'name': 'John Doe', 'email': '<EMAIL>'}});
          //   },
          // ),
          // const SizedBox(height: 8),
          // IconButton(
          //   icon: const Icon(Icons.settings),
          //   tooltip: 'Settings',
          //   onPressed: () {
          //     final queryParams = '?theme=dark&language=en';
          //     context.go('/settings$queryParams', extra: {'settingsData': {'notifications': true}});
          //   },
          // ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            onPressed: () {
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

class _SidebarRail extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
      child: Column(
        children: [
          const SizedBox(height: 16),
          IconButton(
            icon: const Icon(Icons.home),
            tooltip: 'Home',
            onPressed: () {
              context.go('/home');
            },
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.person),
            tooltip: 'Profile',
            onPressed: () {
              // context.go('/profile/123', extra: {'userData': {'name': 'John Doe', 'email': '<EMAIL>'}});
            },
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Settings',
            onPressed: () {
              // final queryParams = '?theme=dark&language=en';
              // context.go('/settings$queryParams', extra: {'settingsData': {'notifications': true}});
            },
          ),
          const SizedBox(height: 8),
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            onPressed: () {
              context.read<AppConfigBloc>().add(LogoutEvent());
            },
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

class MyTextFieldWidget extends StatefulWidget {
  const MyTextFieldWidget({super.key});

  @override
  State<MyTextFieldWidget> createState() => _MyTextFieldWidgetState();
}

class _MyTextFieldWidgetState extends State<MyTextFieldWidget> {
  TextEditingController controller = TextEditingController();
  String? errorText;
  
  @override
  Widget build(BuildContext context) {
    log("================== Widget builder");
    
    // Get the HomeScreen widget to access passed data
    final homeScreen = context.findAncestorWidgetOfExactType<HomeScreen>();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display passed data if available
        if (homeScreen?.userData != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'User Data:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...homeScreen!.userData!.entries.map((entry) => 
                  Text('${entry.key}: ${entry.value}')
                ),
              ],
            ),
          ),
        ],
        
        if (homeScreen?.settingsData != null) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.tertiaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Settings Data:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...homeScreen!.settingsData!.entries.map((entry) => 
                  Text('${entry.key}: ${entry.value}')
                ),
              ],
            ),
          ),
        ],
        
        CommonTextField(
          controller: controller,
          label: 'Enter text',
          onChanged: (value) {
            setState(() {
              errorText = (value.isEmpty) ? 'Field cannot be empty' : null;
            });
            context.read<HomeBloc>().add(UpdateSelectedValueA(value));
          },
        ),
        const SizedBox(height: 20),

        /// ✅ Only this widget rebuilds when inputValue changes
        BlocConsumer<HomeBloc, HomeState>(
          listener: (context, state) {},
          // selector: (state) => state.inputValue,
          builder: (context, state) {
            log("=============== consumer builder");
            return Column(
              children: [
                AppDropdown(
                  items: state.dropdownItemsA,
                  value: state.selectedValueA,
                  hint: 'Select A',
                  isLoading: state.isLoadingA,
                  isError: false, // Add error logic if needed
                  onChanged: (value) {
                    if (value != null) {
                      context.read<HomeBloc>().add(UpdateSelectedValueA(value));
                    }
                  },
                ),
                SizedBox(height: 20,),
                Text('Data: ${state.selectedValueA}',style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 25),),
              ],
            );
          },
        ),
      ],
    );
  }
}

