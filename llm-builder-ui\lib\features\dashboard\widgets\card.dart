import 'dart:convert';
import 'package:flutter/material.dart';
import 'dart:math' as math;

// Data model for chart points
class ChartDataPoint {
  final String label;
  final double value;
  final Color color;

  const ChartDataPoint({
    required this.label,
    required this.value,
    required this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      'color': SerializationHelper.colorToJson(color),
    };
  }

  static ChartDataPoint fromJson(Map<String, dynamic> json) {
    return ChartDataPoint(
      label: json['label'],
      value: json['value']?.toDouble() ?? 0.0,
      color: SerializationHelper.colorFromJson(json['color']),
    );
  }
}

// Enhanced Chart Card Widget
class ChartCard extends StatelessWidget {
  final String? title;
  final String? totalValue;
  final List<ChartDataPoint>? dataPoints;
  final CardSize cardSize;
  final Color backgroundColor;
  final EdgeInsets padding;
  final BorderRadius borderRadius;

   const ChartCard({
    super.key,
     this.title,
     this.totalValue,
     this.dataPoints,
    this.cardSize = CardSize.medium,
    this.backgroundColor = Colors.white,
    this.padding = const EdgeInsets.all(16),
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
  });

  @override
  Widget build(BuildContext context) {
    final isSmall = cardSize == CardSize.small;
    final chartSize = isSmall ? 120.0 : 180.0;

    return Card(
      color: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: borderRadius),
      elevation: 2,
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title??'',
              style: TextStyle(
                fontSize: isSmall ? 14 : 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                // Donut Chart
                SizedBox(
                  width: chartSize,
                  height: chartSize,
                  child: Stack(
                    children: [
                      CustomPaint(
                        size: Size(chartSize, chartSize),
                        painter: DonutChartPainter(dataPoints??[]),
                      ),
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Total Value',
                              style: TextStyle(
                                fontSize: isSmall ? 10 : 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              totalValue??"",
                              style: TextStyle(
                                fontSize: isSmall ? 16 : 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                // Legend
                Expanded(
                  child: _buildLegend(isSmall),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegend(bool isSmall) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: (dataPoints??[]).map((point) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: point.color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                point.label,
                style: TextStyle(
                  fontSize: isSmall ? 11 : 12,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': 'ChartCard',
      'title': title,
      'totalValue': totalValue,
      'dataPoints': (dataPoints??[]).map((point) => point.toJson()).toList(),
      'cardSize': cardSize.name,
      'backgroundColor': SerializationHelper.colorToJson(backgroundColor),
      'padding': SerializationHelper.edgeInsetsToJson(padding),
      'borderRadius': _borderRadiusToJson(borderRadius),
    };
  }

  static ChartCard fromJson(Map<String, dynamic> json) {
    return ChartCard(
      title: json['title'],
      totalValue: json['totalValue'],
      dataPoints: (json['dataPoints'] as List<dynamic>)
          .map((pointJson) => ChartDataPoint.fromJson(pointJson))
          .toList(),
      cardSize: CardSize.values.firstWhere(
            (size) => size.name == json['cardSize'],
        orElse: () => CardSize.medium,
      ),
      backgroundColor: SerializationHelper.colorFromJson(json['backgroundColor']),
      padding: SerializationHelper.edgeInsetsFromJson(json['padding']),
      borderRadius: _borderRadiusFromJson(json['borderRadius']),
    );
  }

  Map<String, dynamic> _borderRadiusToJson(BorderRadius borderRadius) {
    return {
      'topLeft': borderRadius.topLeft.x,
      'topRight': borderRadius.topRight.x,
      'bottomLeft': borderRadius.bottomLeft.x,
      'bottomRight': borderRadius.bottomRight.x,
    };
  }

  static BorderRadius _borderRadiusFromJson(Map<String, dynamic> json) {
    return BorderRadius.only(
      topLeft: Radius.circular(json['topLeft']?.toDouble() ?? 8),
      topRight: Radius.circular(json['topRight']?.toDouble() ?? 8),
      bottomLeft: Radius.circular(json['bottomLeft']?.toDouble() ?? 8),
      bottomRight: Radius.circular(json['bottomRight']?.toDouble() ?? 8),
    );
  }
}

// Enum for card sizes
enum CardSize { small, medium, large }

// Custom painter for donut chart
class DonutChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;

  DonutChartPainter(this.dataPoints);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;
    final innerRadius = radius * 0.6;

    final total = dataPoints.fold(0.0, (sum, point) => sum + point.value);

    double startAngle = -math.pi / 2;

    for (final point in dataPoints) {
      final sweepAngle = (point.value / total) * 2 * math.pi;

      final paint = Paint()
        ..color = point.color
        ..style = PaintingStyle.stroke
        ..strokeWidth = radius - innerRadius;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: (radius + innerRadius) / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Enhanced Widget Factory
class EnhancedWidgetFactory {
  static Widget? createFromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'CustomButton':
        return CustomButton.fromJson(json);
      case 'CustomCard':
        return CustomCard.fromJson(json);
      case 'ChartCard':
        return ChartCard.fromJson(json);
      default:
        return null;
    }
  }
}

// Enhanced example usage
class EnhancedWidgetJsonExample extends StatefulWidget {
  const EnhancedWidgetJsonExample({super.key});

  @override
  State<EnhancedWidgetJsonExample> createState() => _EnhancedWidgetJsonExampleState();
}

class _EnhancedWidgetJsonExampleState extends State<EnhancedWidgetJsonExample> {
  String jsonOutput = '';
  Widget? reconstructedWidget;

  void createTemperatureCards() {
    // Create sample data like in your image
    final mediumCardData = [
      ChartDataPoint(label: 'Point 1', value: 25, color: Color(0xFF2196F3)),
      ChartDataPoint(label: 'Point 2', value: 30, color: Color(0xFF1976D2)),
      ChartDataPoint(label: 'Point 3', value: 20, color: Color(0xFF0D47A1)),
      ChartDataPoint(label: 'Point 4', value: 15, color: Color(0xFF42A5F5)),
      ChartDataPoint(label: 'Point 5', value: 35, color: Color(0xFFBBDEFB)),
      ChartDataPoint(label: 'Point 6', value: 28, color: Color(0xFF90CAF9)),
      ChartDataPoint(label: 'Point 7', value: 22, color: Color(0xFF64B5F6)),
      ChartDataPoint(label: 'Point 8', value: 18, color: Color(0xFF2196F3)),
    ];

    final chartCard = ChartCard(
      title: 'Temperature theme',
      totalValue: '\$9,999.99',
      dataPoints: mediumCardData,
      cardSize: CardSize.medium,
      backgroundColor: Colors.white,
    );

    // Convert to JSON
    final json = chartCard.toJson();
    final jsonString = const JsonEncoder.withIndent('  ').convert(json);

    setState(() {
      jsonOutput = jsonString;
      reconstructedWidget = EnhancedWidgetFactory.createFromJson(json);
    });
  }

  @override
  Widget build(BuildContext context) {
    return  SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Demo cards like in your image
            const Text(
              'Cards',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(child: const Text('Medium', style: TextStyle(fontSize: 18))),
                // const SizedBox(width: 100),
                Expanded(child: const Text('Small', style: TextStyle(fontSize: 18))),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Medium card
                Expanded(
                  child: ChartCard(
                    title: 'Temperature theme',
                    totalValue: '\$9,999.99',
                    dataPoints: [
                      ChartDataPoint(label: 'Point 1', value: 25, color: Color(0xFF2196F3)),
                      ChartDataPoint(label: 'Point 2', value: 30, color: Color(0xFF1976D2)),
                      ChartDataPoint(label: 'Point 3', value: 20, color: Color(0xFF0D47A1)),
                      ChartDataPoint(label: 'Point 4', value: 15, color: Color(0xFF42A5F5)),
                      ChartDataPoint(label: 'Point 5', value: 35, color: Color(0xFFBBDEFB)),
                      ChartDataPoint(label: 'Point 6', value: 28, color: Color(0xFF90CAF9)),
                      ChartDataPoint(label: 'Point 7', value: 22, color: Color(0xFF64B5F6)),
                      ChartDataPoint(label: 'Point 8', value: 18, color: Color(0xFF2196F3)),
                    ],
                    cardSize: CardSize.medium,
                  ),
                ),
                const SizedBox(width: 20),
                // Small card
                Expanded(
                  child: ChartCard(
                    title: 'Temperature theme',
                    totalValue: '\$9,999.99',
                    dataPoints: [
                      ChartDataPoint(label: 'Point 1', value: 25, color: Color(0xFF2196F3)),
                      ChartDataPoint(label: 'Point 2', value: 30, color: Color(0xFF1976D2)),
                      ChartDataPoint(label: 'Point 3', value: 20, color: Color(0xFF0D47A1)),
                      ChartDataPoint(label: 'Point 4', value: 15, color: Color(0xFF42A5F5)),
                      ChartDataPoint(label: 'Point 5', value: 35, color: Color(0xFFBBDEFB)),
                      ChartDataPoint(label: 'Point 6', value: 28, color: Color(0xFF90CAF9)),
                      ChartDataPoint(label: 'Point 7', value: 22, color: Color(0xFF64B5F6)),
                      ChartDataPoint(label: 'Point 8', value: 18, color: Color(0xFF2196F3)),
                    ],
                    cardSize: CardSize.small,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: createTemperatureCards,
              child: const Text('Convert Chart Card to JSON'),
            ),
            const SizedBox(height: 20),
            if (jsonOutput.isNotEmpty) ...[
              const Text(
                'JSON Output:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    jsonOutput,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Reconstructed Widget:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 10),
              if (reconstructedWidget != null)
                SizedBox(
                  width: 400,
                  child: reconstructedWidget,
                ),
            ],
          ],
        ),
      );
    
  }
}

// Keep the original CustomButton and CustomCard classes from your code
class CustomButton extends StatelessWidget {
  final String text;
  final Color color;
  final double width;
  final double height;
  final VoidCallback? onPressed;
  final IconData? icon;

  const CustomButton({
    super.key,
    required this.text,
    this.color = Colors.blue,
    this.width = 200,
    this.height = 50,
    this.onPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: icon != null ? Icon(icon) : const SizedBox.shrink(),
        label: Text(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
        ),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': 'CustomButton',
      'text': text,
      'color': SerializationHelper.colorToJson(color),
      'width': width,
      'height': height,
      'icon': icon?.codePoint,
    };
  }

  static CustomButton fromJson(Map<String, dynamic> json) {
    return CustomButton(
      text: json['text'],
      color: SerializationHelper.colorFromJson(json['color']),
      width: json['width']?.toDouble() ?? 200,
      height: json['height']?.toDouble() ?? 50,
      icon: json['icon'] != null ? IconData(json['icon']) : null,
    );
  }
}

class CustomCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? imageUrl;
  final EdgeInsets padding;
  final BorderRadius borderRadius;
  final Color backgroundColor;
  final List<CustomButton> buttons;

  const CustomCard({
    super.key,
    required this.title,
    required this.subtitle,
    this.imageUrl,
    this.padding = const EdgeInsets.all(16),
    this.borderRadius = const BorderRadius.all(Radius.circular(8)),
    this.backgroundColor = Colors.white,
    this.buttons = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: borderRadius),
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (imageUrl != null)
              Image.network(imageUrl??"", height: 150, fit: BoxFit.cover),
            Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text(subtitle, style: const TextStyle(fontSize: 14)),
            const SizedBox(height: 16),
            ...buttons,
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': 'CustomCard',
      'title': title,
      'subtitle': subtitle,
      'imageUrl': imageUrl,
      'padding': SerializationHelper.edgeInsetsToJson(padding),
      'borderRadius': {
        'topLeft': borderRadius.topLeft.x,
        'topRight': borderRadius.topRight.x,
        'bottomLeft': borderRadius.bottomLeft.x,
        'bottomRight': borderRadius.bottomRight.x,
      },
      'backgroundColor': SerializationHelper.colorToJson(backgroundColor),
      'buttons': buttons.map((button) => button.toJson()).toList(),
    };
  }

  static CustomCard fromJson(Map<String, dynamic> json) {
    return CustomCard(
      title: json['title'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      padding: SerializationHelper.edgeInsetsFromJson(json['padding']),
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(json['borderRadius']['topLeft']?.toDouble() ?? 8),
        topRight: Radius.circular(json['borderRadius']['topRight']?.toDouble() ?? 8),
        bottomLeft: Radius.circular(json['borderRadius']['bottomLeft']?.toDouble() ?? 8),
        bottomRight: Radius.circular(json['borderRadius']['bottomRight']?.toDouble() ?? 8),
      ),
      backgroundColor: SerializationHelper.colorFromJson(json['backgroundColor']),
      buttons: (json['buttons'] as List<dynamic>?)
          ?.map((buttonJson) => CustomButton.fromJson(buttonJson))
          .toList() ??
          [],
    );
  }
}

// Enhanced serialization helper
class SerializationHelper {
  static Map<String, dynamic> colorToJson(Color color) {
    return {
      'red': color.red,
      'green': color.green,
      'blue': color.blue,
      'alpha': color.alpha,
    };
  }

  static Color colorFromJson(Map<String, dynamic> json) {
    return Color.fromARGB(
      json['alpha'] ?? 255,
      json['red'] ?? 0,
      json['green'] ?? 0,
      json['blue'] ?? 0,
    );
  }

  static Map<String, dynamic> edgeInsetsToJson(EdgeInsets insets) {
    return {
      'left': insets.left,
      'top': insets.top,
      'right': insets.right,
      'bottom': insets.bottom,
    };
  }

  static EdgeInsets edgeInsetsFromJson(Map<String, dynamic> json) {
    return EdgeInsets.only(
      left: json['left']?.toDouble() ?? 0,
      top: json['top']?.toDouble() ?? 0,
      right: json['right']?.toDouble() ?? 0,
      bottom: json['bottom']?.toDouble() ?? 0,
    );
  }
}