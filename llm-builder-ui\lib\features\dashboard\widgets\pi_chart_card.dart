// import 'package:flutter/material.dart';
// import 'package:pie_chart/pie_chart.dart';

// class DonutChartCard extends StatelessWidget {
//   final String sizeLabel;
//   final double headingFontSize;
//   final double bodyFontSize;
//   final double labelFontSize;
//   final double chartRadius;

//   const DonutChartCard({
//     super.key,
//     required this.sizeLabel,
//     required this.headingFontSize,
//     required this.bodyFontSize,
//     required this.labelFontSize,
//     required this.chartRadius,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final List<Color> sectionColors = [
//       Colors.blue.shade700,
//       Colors.blue.shade500,
//       Colors.blue.shade400,
//       Colors.blue.shade300,
//       Colors.blue.shade200,
//       Colors.blue.shade100,
//       Colors.blue.shade50,
//       Colors.blue.shade800,
//     ];
//     final List<String> legendLabels = [
//       'Point 1', 'Point 2', 'Point 3', 'Point 4',
//       'Point 5', 'Point 6', 'Point 7', 'Point 8',
//     ];
//     final Map<String, double> dataMap = {
//       'Point 1': 2,
//       'Point 2': 2,
//       'Point 3': 2,
//       'Point 4': 2,
//       'Point 5': 2,
//       'Point 6': 2,
//       'Point 7': 2,
//       'Point 8': 2,
//     };

//     return Card(
//       elevation: 0.5,
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//       child: Padding(
//         padding: const EdgeInsets.all(20.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Temperature theme',
//               style: TextStyle(
//                 fontWeight: FontWeight.w600,
//                 fontSize: bodyFontSize,
//                 color: Colors.grey[800],
//               ),
//             ),
//             const SizedBox(height: 8),
//             Row(
//               children: [
//                 SizedBox(
//                   width: chartRadius * 2,
//                   height: chartRadius * 2,
//                   child: PieChart(
//                     dataMap: dataMap,
//                     colorList: sectionColors,
//                     chartType: ChartType.ring,
//                     ringStrokeWidth: chartRadius * 0.3,
//                     // chartRadius: 10,
//                     legendOptions: const LegendOptions(showLegends: false),
//                     chartValuesOptions: const ChartValuesOptions(showChartValues: false),
//                     // centerText: 'Total Value\n9,999.99',
//                     centerWidget: Text("Total Value\n9,999.99",style: Theme.of(context).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.bold),),
                    
//                   ),
//                 ),
//                 const SizedBox(width: 24),
//                 // Custom legend: 2 columns, 4 rows, each row is (i, i+4)
//                 SizedBox(
//                   width: 160,
//                   height: chartRadius * 2,
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: List.generate(4, (row) => Padding(
//                       padding: const EdgeInsets.symmetric(vertical: 4),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.start,
//                         children: [
//                           Container(width: 14, height: 14, color: sectionColors[row]),
//                           const SizedBox(width: 6),
//                           Text(legendLabels[row], style: TextStyle(fontSize: labelFontSize)),
//                           const SizedBox(width: 18),
//                           Container(width: 14, height: 14, color: sectionColors[row+4]),
//                           const SizedBox(width: 6),
//                           Text(legendLabels[row+4], style: TextStyle(fontSize: labelFontSize)),
//                         ],
//                       ),
//                     )),
//                   ),
//                 ),
//               ],
//             ),
//             // const SizedBox(height: 24),
//             // Text('Properties', style: TextStyle(fontWeight: FontWeight.bold, fontSize: bodyFontSize, decoration: TextDecoration.underline)),
//             // Text('Heading 1 medium ${headingFontSize.toInt()}', style: TextStyle(fontWeight: FontWeight.w500, fontSize: headingFontSize)),
//             // Text('Body 1 Regular ${bodyFontSize.toInt()}', style: TextStyle(fontSize: bodyFontSize)),
//             // Text('Label - Regular ${labelFontSize.toInt()}', style: TextStyle(fontSize: labelFontSize, color: Colors.grey[700])),
//           ],
//         ),
//       ),
//     );
//   }
// } 

import 'package:flutter/material.dart';
import 'package:pie_chart/pie_chart.dart';

class RingPieChartExample extends StatelessWidget {
  final Map<String, double> dataMap = {
    "Flutter": 5,
    "React": 3,
    "Xamarin": 2,
  };

  final List<Color> colorList = [
    Colors.blue,
    Colors.green,
    Colors.orange,
  ];

  @override
  Widget build(BuildContext context) {
    return  Center(
        child: PieChart(
          dataMap: dataMap,
          animationDuration: Duration(milliseconds: 800),
          chartRadius: 100,
          colorList: colorList,
          chartType: ChartType.ring,
          ringStrokeWidth: 32,
          centerText: "Tech",
          chartValuesOptions: ChartValuesOptions(
          showChartValues: false,
           showChartValuesInPercentage: false,
            ),
          legendOptions: LegendOptions(
            // showLegends: true,
            legendPosition: LegendPosition.bottom,
          ),
         
        ),
     
    );
  }
}
